/**
 * 智能反馈系统
 * 实现动作阶段识别和个性化反馈生成
 */
import { ref, computed } from "vue";

export function useActionFeedback() {
  // 反馈状态
  const currentFeedback = ref({
    text: "准备开始训练",
    type: "info", // info, success, warning, error
    priority: "normal", // low, normal, high, urgent
    timestamp: Date.now(),
  });

  // 反馈历史
  const feedbackHistory = ref([]);

  // 反馈频率控制
  const lastFeedbackTime = ref(0);
  const feedbackCooldown = ref(2000); // 2秒冷却时间

  // 动作阶段定义
  const ACTION_STAGES = {
    // 通用阶段
    incomplete: { priority: "urgent", type: "error" },
    invalid: { priority: "high", type: "error" },
    preparing: { priority: "normal", type: "info" },
    completed: { priority: "high", type: "success" },
    retry: { priority: "normal", type: "warning" },

    // 肩部触摸阶段
    too_far: { priority: "normal", type: "info" },
    approaching: { priority: "normal", type: "info" },
    good_touch: { priority: "normal", type: "success" },
    perfect_touch: { priority: "high", type: "success" },

    // 手臂上举阶段
    too_low: { priority: "normal", type: "info" },
    starting: { priority: "normal", type: "info" },
    raising: { priority: "normal", type: "info" },
    good_height: { priority: "normal", type: "success" },
    perfect_height: { priority: "high", type: "success" },

    // 指尖对触阶段
    good_touch: { priority: "normal", type: "success" },
    perfect_touch: { priority: "high", type: "success" },

    // 手掌翻转阶段
    moving: { priority: "normal", type: "info" },
    flipping: { priority: "normal", type: "info" },
    good_flipping: { priority: "normal", type: "success" },
    excellent: { priority: "high", type: "success" },
  };

  // 鼓励性词汇库
  const ENCOURAGEMENT_WORDS = {
    start: ["开始吧", "准备好了", "让我们开始"],
    progress: ["很好", "继续", "保持", "不错", "加油"],
    good: ["做得好", "很棒", "优秀", "太好了", "非常好"],
    perfect: ["完美", "太棒了", "非常完美", "出色", "卓越"],
  };

  // 指导性词汇库
  const GUIDANCE_WORDS = {
    move: ["移动", "靠近", "抬起", "放下", "转动"],
    direction: ["向上", "向下", "向左", "向右", "向前", "向后"],
    speed: ["慢慢地", "缓慢地", "稳定地", "轻柔地", "平稳地"],
    maintain: ["保持", "维持", "稳住", "不要动", "停在这里"],
  };

  /**
   * 生成智能反馈
   * @param {Object} detectionResult - 检测结果
   * @param {string} actionType - 动作类型
   * @param {string} side - 动作侧面
   * @param {number} accuracy - 准确度分数
   * @returns {Object} - 反馈对象
   */
  const generateFeedback = (detectionResult, actionType, side, accuracy) => {
    const now = Date.now();

    // 检查冷却时间
    if (
      now - lastFeedbackTime.value < feedbackCooldown.value &&
      detectionResult.stage !== "incomplete" &&
      detectionResult.stage !== "invalid"
    ) {
      return currentFeedback.value;
    }

    // 获取阶段配置
    const stageConfig = ACTION_STAGES[detectionResult.stage] || {
      priority: "normal",
      type: "info",
    };

    // 生成个性化反馈文本
    const feedbackText = generatePersonalizedText(
      detectionResult,
      actionType,
      side,
      accuracy
    );

    // 创建反馈对象
    const feedback = {
      text: feedbackText,
      type: stageConfig.type,
      priority: stageConfig.priority,
      timestamp: now,
      stage: detectionResult.stage,
      accuracy,
    };

    // 更新当前反馈
    currentFeedback.value = feedback;

    // 添加到历史记录
    addToHistory(feedback);

    // 更新最后反馈时间
    lastFeedbackTime.value = now;

    return feedback;
  };

  /**
   * 生成个性化反馈文本
   * @param {Object} detectionResult - 检测结果
   * @param {string} actionType - 动作类型
   * @param {string} side - 动作侧面
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 反馈文本
   */
  const generatePersonalizedText = (
    detectionResult,
    actionType,
    side,
    accuracy
  ) => {
    // 如果检测结果已有反馈，优先使用
    if (detectionResult.feedback) {
      return enhanceBasicFeedback(detectionResult.feedback, accuracy);
    }

    // 根据动作类型和阶段生成反馈
    const sideText = side === "left" ? "左" : "右";

    switch (actionType) {
      case "shoulder_touch":
        return generateShoulderTouchFeedback(
          detectionResult,
          sideText,
          accuracy
        );
      case "arm_raise":
        return generateArmRaiseFeedback(detectionResult, sideText, accuracy);
      case "finger_touch":
        return generateFingerTouchFeedback(detectionResult, accuracy);
      case "palm_flip":
        return generatePalmFlipFeedback(detectionResult, sideText, accuracy);
      default:
        return "请按照示范动作进行";
    }
  };

  /**
   * 增强基础反馈文本
   * @param {string} basicFeedback - 基础反馈
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 增强后的反馈
   */
  const enhanceBasicFeedback = (basicFeedback, accuracy) => {
    if (accuracy >= 90) {
      const encouragement = getRandomWord(ENCOURAGEMENT_WORDS.perfect);
      return `${encouragement}！${basicFeedback}`;
    } else if (accuracy >= 70) {
      const encouragement = getRandomWord(ENCOURAGEMENT_WORDS.good);
      return `${encouragement}！${basicFeedback}`;
    } else if (accuracy >= 40) {
      const encouragement = getRandomWord(ENCOURAGEMENT_WORDS.progress);
      return `${encouragement}，${basicFeedback}`;
    }

    return basicFeedback;
  };

  /**
   * 生成肩部触摸反馈
   * @param {Object} detectionResult - 检测结果
   * @param {string} sideText - 侧面文本
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 反馈文本
   */
  const generateShoulderTouchFeedback = (
    detectionResult,
    sideText,
    accuracy
  ) => {
    const targetSide = sideText === "左" ? "右肩" : "左肩";
    const handSide = sideText === "左" ? "左手" : "右手";

    switch (detectionResult.stage) {
      case "too_far":
        return `请将${handSide}向${targetSide}移动`;
      case "preparing":
        const speed = getRandomWord(GUIDANCE_WORDS.speed);
        return `${speed}将${handSide}向${targetSide}靠近`;
      case "approaching":
        const progress = getRandomWord(ENCOURAGEMENT_WORDS.progress);
        return `${progress}！${handSide}快要触摸到${targetSide}了`;
      case "good_touch":
        const good = getRandomWord(ENCOURAGEMENT_WORDS.good);
        return `${good}！${handSide}成功触摸${targetSide}`;
      case "perfect_touch":
        const perfect = getRandomWord(ENCOURAGEMENT_WORDS.perfect);
        return `${perfect}！${handSide}完美触摸${targetSide}`;
      default:
        return `请将${handSide}触摸${targetSide}`;
    }
  };

  /**
   * 生成手臂上举反馈
   * @param {Object} detectionResult - 检测结果
   * @param {string} sideText - 侧面文本
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 反馈文本
   */
  const generateArmRaiseFeedback = (detectionResult, sideText, accuracy) => {
    const armText = `${sideText}臂`;

    switch (detectionResult.stage) {
      case "too_low":
        return `请抬起${armText}`;
      case "starting":
        const progress = getRandomWord(ENCOURAGEMENT_WORDS.progress);
        return `${progress}！继续抬高${armText}`;
      case "raising":
        const speed = getRandomWord(GUIDANCE_WORDS.speed);
        return `${speed}将${armText}举得更高`;
      case "good_height":
        const good = getRandomWord(ENCOURAGEMENT_WORDS.good);
        return `${good}！${armText}高度很好`;
      case "perfect_height":
        const perfect = getRandomWord(ENCOURAGEMENT_WORDS.perfect);
        return `${perfect}！${armText}高度完美`;
      default:
        return `请将${armText}向上举起`;
    }
  };

  /**
   * 生成指尖对触反馈
   * @param {Object} detectionResult - 检测结果
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 反馈文本
   */
  const generateFingerTouchFeedback = (detectionResult, accuracy) => {
    const isWristFallback =
      detectionResult.detectionMethod === "wrist_fallback";
    const actionText = isWristFallback ? "双手" : "指尖";

    switch (detectionResult.stage) {
      case "too_far":
        return `请将${actionText}相互靠近`;
      case "preparing":
        const speed = getRandomWord(GUIDANCE_WORDS.speed);
        return `${speed}将${actionText}靠近`;
      case "approaching":
        const progress = getRandomWord(ENCOURAGEMENT_WORDS.progress);
        return `${progress}！${actionText}快要接触了`;
      case "good_touch":
        const good = getRandomWord(ENCOURAGEMENT_WORDS.good);
        return `${good}！${actionText}成功对触`;
      case "perfect_touch":
        const perfect = getRandomWord(ENCOURAGEMENT_WORDS.perfect);
        return `${perfect}！${actionText}完美对触`;
      default:
        return `请将${actionText}相互接触`;
    }
  };

  /**
   * 生成手掌翻转反馈
   * @param {Object} detectionResult - 检测结果
   * @param {string} sideText - 侧面文本
   * @param {number} accuracy - 准确度分数
   * @returns {string} - 反馈文本
   */
  const generatePalmFlipFeedback = (detectionResult, sideText, accuracy) => {
    const isFallback = detectionResult.detectionMethod === "fallback";
    const actionText = isFallback ? "前臂" : "手掌";
    const handText = `${sideText}手`;

    switch (detectionResult.stage) {
      case "preparing":
        return `请开始${handText}${actionText}翻转`;
      case "moving":
        return `继续${actionText}翻转，加大动作幅度`;
      case "flipping":
        const progress = getRandomWord(ENCOURAGEMENT_WORDS.progress);
        return `${progress}！继续${actionText}翻转`;
      case "good_flipping":
        const good = getRandomWord(ENCOURAGEMENT_WORDS.good);
        return `${good}！${actionText}翻转节奏很好`;
      case "excellent":
        const perfect = getRandomWord(ENCOURAGEMENT_WORDS.perfect);
        return `${perfect}！${actionText}翻转非常出色`;
      default:
        return `请进行${handText}${actionText}翻转动作`;
    }
  };

  /**
   * 从词汇库中随机选择一个词
   * @param {Array} words - 词汇数组
   * @returns {string} - 随机选择的词
   */
  const getRandomWord = (words) => {
    return words[Math.floor(Math.random() * words.length)];
  };

  /**
   * 添加反馈到历史记录
   * @param {Object} feedback - 反馈对象
   */
  const addToHistory = (feedback) => {
    feedbackHistory.value.push(feedback);

    // 保持历史记录在合理范围内
    if (feedbackHistory.value.length > 50) {
      feedbackHistory.value = feedbackHistory.value.slice(-25);
    }
  };

  /**
   * 设置反馈冷却时间
   * @param {number} cooldown - 冷却时间（毫秒）
   */
  const setFeedbackCooldown = (cooldown) => {
    feedbackCooldown.value = cooldown;
  };

  /**
   * 重置反馈状态
   */
  const resetFeedback = () => {
    currentFeedback.value = {
      text: "准备开始训练",
      type: "info",
      priority: "normal",
      timestamp: Date.now(),
    };
    feedbackHistory.value = [];
    lastFeedbackTime.value = 0;
  };

  // 计算属性
  const shouldShowFeedback = computed(() => {
    return (
      currentFeedback.value.priority === "urgent" ||
      currentFeedback.value.priority === "high" ||
      Date.now() - currentFeedback.value.timestamp < 5000
    );
  });

  const feedbackColorClass = computed(() => {
    const colorMap = {
      info: "text-blue-200",
      success: "text-green-200",
      warning: "text-yellow-200",
      error: "text-red-200",
    };
    return colorMap[currentFeedback.value.type] || "text-gray-200";
  });

  return {
    // 响应式数据
    currentFeedback,
    feedbackHistory,

    // 计算属性
    shouldShowFeedback,
    feedbackColorClass,

    // 方法
    generateFeedback,
    setFeedbackCooldown,
    resetFeedback,

    // 配置
    ACTION_STAGES,
    ENCOURAGEMENT_WORDS,
    GUIDANCE_WORDS,
  };
}

export { useActionFeedback };
